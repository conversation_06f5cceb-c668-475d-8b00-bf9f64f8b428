//#include <stm32f4xx_hal.h> //Sets up the correct chip specifc defines required by arm_math
//#define ARM_MATH_CM4
//#include <arm_math.h>

#include <mcpwm.h>

#include <stdlib.h>
#include <math.h>
//#include <cmsis_os.h>

#include <main.h>
#include <adc.h>
#include <tim.h>
#include <spi.h>
#include <utils.h>
#include "delay.h"
#include "bmp.h"

s32 profile_acce=5000,profile_dece=5000,profile_speed=5000,end_speed=0,profile_speed_b,target_speed_now=0,direction=1;
s32 profile_target_position,profile_target_position_b;
s32 home_offest=0,homing_speed=0,homing_acce=0;
s8 homing_method=0;
s32 acce_diatance,dece_diatance,distance_diff=0,decelerating_position=0,target_pos_now=0,searching_speed=0;
int64_t target_pos_diff;
u8 accelerating=0,decelerating=0,positionning=0,motion_state=0;
int64_t remain_dst=0,step_dst=0;
s32 auto_p_pos=5000,auto_n_pos=-5000;
u32 auto_reverse_p_time=0,auto_reverse_n_time=0,auto_reverse_time=0,auto_reverse_status;
u16 auto_switch_on=0;
int pluse_num=0,pluse_num_r=0,pul_dir=0,gear_factor_a=100,gear_factor_b=10,pluse_num_beforegear=0;
u16 pluse_temp=0,pluse_temp_b=0,delta_pulse=0,delta_pulse_r=0;
short motion_out_lpf_a=1000;
char display_buff[40],display_buff1[40],display_buff2[40],display_buff3[40],display_buff4[40];
short OLED_count=0,OLED_Period=100;

int EN_state=0,EN_state_b=0,DIN1_state=0,DIN1_state_b=0,DIN2_state=0,DIN2_state_b=0,DIN3_state=0,DIN3_state_b=0,DOUT1_state=0,DOUT1_state_b=0;

s32 Acce_distance_cal(int acce,int speed)
{
	int64_t v,a,r,d;
	v=speed;
	a=acce;
	r=feedback_resolution;
	d=(v*v*r)/(2000*a);
	return d;
}
void Motion_process(void)
{
	int64_t temp64a,temp64b;
	switch(operation_mode)
	{
		case 1:
		case 11:
			switch(motion_state)
			{
				case 1://accelerating
					remain_dst=direction*(profile_target_position_b-target_pos_now);
					dece_diatance=Acce_distance_cal(profile_dece,(target_speed_now/1000));
					if(remain_dst<dece_diatance)
					{
						decelerating=1;
						accelerating=0;
						motion_state=3;
					}
					target_speed_now+=profile_acce;
					if(target_speed_now>(profile_speed*1000))
					{
						target_speed_now=profile_speed*1000;
						accelerating=0;
						motion_state=2;
					}
					temp64a=target_speed_now;
					temp64b=feedback_resolution;
					step_dst=(temp64a*temp64b)/1000000000;
					target_pos_now=target_pos_now+direction*step_dst;
					break;
				case 2://const speed
					remain_dst=direction*(profile_target_position_b-target_pos_now);
					dece_diatance=Acce_distance_cal(profile_dece,(target_speed_now/1000));
					if(remain_dst<dece_diatance)
					{
						decelerating=1;
						accelerating=0;
						motion_state=3;
					}
					temp64a=target_speed_now;
					temp64b=feedback_resolution;
					step_dst=(temp64a*temp64b)/1000000000;
					target_pos_now=target_pos_now+direction*step_dst;
					break;
				case 3://decelerating
					remain_dst=direction*(profile_target_position_b-target_pos_now);
					//dece_diatance=Acce_distance_cal(profile_dece,(target_speed_now/1000));
					temp64a=(2*profile_dece*remain_dst*1000)/feedback_resolution;
					temp64b= sqrt(temp64a);
					target_speed_now=1000*temp64b;
				
					if(target_speed_now<(searching_speed*1000))
					{
						target_speed_now=searching_speed*1000;
						decelerating=0;
						motion_state=4;
					}
				
					temp64a=target_speed_now;
					temp64b=feedback_resolution;
					step_dst=(temp64a*temp64b)/1000000000;
					target_pos_now=target_pos_now+direction*step_dst;
					if(direction*target_pos_now>direction*profile_target_position_b)
					{
						target_pos_now=profile_target_position_b;
						decelerating=0;
						positionning=0;
						motion_state=0;
					}
					break;
				case 4://searching
					temp64a=target_speed_now;
					temp64b=feedback_resolution;
					step_dst=(temp64a*temp64b)/1000000000;
					target_pos_now=target_pos_now+direction*step_dst;
					if(direction*target_pos_now>=direction*profile_target_position_b)
					{
						target_pos_now=profile_target_position_b;
						decelerating=0;
						positionning=0;
						motion_state=0;
					}
					break;
				default:
					break;
			}
			
			position_demand=target_pos_now;
			
			if((target_position!=profile_target_position_b)&&(motion_state==0))
			{
				profile_target_position_b=target_position;
				positionning=1;
				distance_diff=target_position-pos_actual;
				if(distance_diff>=0)
				{
					direction=1;
					acce_diatance=Acce_distance_cal(profile_acce,profile_speed);// ����Ӽ��پ���
					dece_diatance=Acce_distance_cal(profile_dece,profile_speed);
					accelerating=1;
					decelerating=0;
					motion_state=1;
				}
				if(distance_diff<0)
				{
					direction=-1;
					acce_diatance=Acce_distance_cal(profile_acce,profile_speed);
					dece_diatance=Acce_distance_cal(profile_dece,profile_speed);
					accelerating=1;
					decelerating=0;
					motion_state=1;
				}
			}
			
			break;
		case 3:
			if(target_speed!=profile_speed_b)
			{
				profile_speed_b=target_speed;
			}
			if(profile_speed_b>=0)
			{
				if(target_speed_now<profile_speed_b*1000)
				{
					target_speed_now+=profile_acce;
					if(target_speed_now>profile_speed_b*1000)
						target_speed_now=profile_speed_b*1000;
				}
				if(target_speed_now>profile_speed_b*1000)
				{
					target_speed_now-=profile_dece;
					if(target_speed_now<profile_speed_b*1000)
						target_speed_now=profile_speed_b*1000;
				}
			}
			if(profile_speed_b<0)
			{
				if(target_speed_now>profile_speed_b*1000)
				{
					target_speed_now-=profile_acce;
					if(target_speed_now<profile_speed_b*1000)
						target_speed_now=profile_speed_b*1000;
				}
				if(target_speed_now<profile_speed_b*1000)
				{
					target_speed_now+=profile_dece;
					if(target_speed_now>profile_speed_b*1000)
						target_speed_now=profile_speed_b*1000;
				}
			}
			
			temp64a=target_speed_now;
			temp64b=feedback_resolution;
			step_dst=(temp64a*temp64b)/1000000000;
			target_pos_now=target_pos_now+step_dst;
			
			position_demand=target_pos_now;
			break;
		case 7://7
			position_demand=target_position;
			break;
		case 2://2
			speed_demand=target_speed;
			break;
		case 0:
			speed_demand=target_speed;
			Iq_demand=target_Iq;
			break;
		case 4:
			Iq_demand=target_Iq;
			
			break;
		default:
			break;
	}
	
}


void Auto_reserve_process(void)
{
	if(auto_switch_on)
	{
		if(motor_on==0)
			if(Error_State.all==0)
				if(control_word.all==0x06)
					control_word.all=0x0f;
	}
	
	if(motor_on)	
	if(auto_reverse_n_time||auto_reverse_p_time)
	{
		switch(auto_reverse_status)
		{
			case 0:
				auto_reverse_time=HAL_GetTick();
				auto_reverse_status=1;
				break;
			case 1:
				if((HAL_GetTick()-auto_reverse_time)>auto_reverse_p_time)
				{
					auto_reverse_time=HAL_GetTick();
					switch(operation_mode)
					{
						case 2:
						case 3:
							target_speed=auto_p_pos;
						break;
						case 1:
						case 7:
							target_position=auto_p_pos;
						break;
						case 4:
							target_Iq=auto_p_pos;
						break;
					}	
					auto_reverse_status=2;
				}
				break;
			case 2:
				if((HAL_GetTick()-auto_reverse_time)>auto_reverse_n_time)
				{
					auto_reverse_time=HAL_GetTick();
					switch(operation_mode)
					{
						case 2:
						case 3:
							target_speed=auto_n_pos;
						break;
						case 1:
						case 7:
							target_position=auto_n_pos;
						break;
						case 4:
							target_Iq=auto_n_pos;
						break;
					}
					auto_reverse_status=1;
				}
				break;
			default:
				break;
		}
	}
	if((auto_reverse_n_time==0)&&(auto_reverse_p_time==0))
	{
		auto_reverse_status=0;
	}

}

void LED_Process(void)
{
		led_blink_counter++;
		if(Error_State.all==0)
		{
			if(motor_on)
			{
				HAL_GPIO_WritePin(LED_2_GPIO_Port, LED_2_Pin, GPIO_PIN_RESET);
				led_blink_period=500-abs(real_speed_filter)/50;
				if(led_blink_period<1)
				{
					led_blink_period=1;
				}
				if(led_blink_counter>led_blink_period)
					led_blink_counter=0;
				if(led_blink_counter>(led_blink_period/2))
				{
					HAL_GPIO_WritePin(LED_1_GPIO_Port, LED_1_Pin, GPIO_PIN_SET);
				}
				else
				{
					HAL_GPIO_WritePin(LED_1_GPIO_Port, LED_1_Pin, GPIO_PIN_RESET);
				}
			}
			else
			{
				HAL_GPIO_WritePin(LED_2_GPIO_Port, LED_2_Pin, GPIO_PIN_RESET);
				led_blink_period=1500;
				if(led_blink_counter>led_blink_period)
					led_blink_counter=0;
				if(led_blink_counter>(led_blink_period/2))
				{
					HAL_GPIO_WritePin(LED_1_GPIO_Port, LED_1_Pin, GPIO_PIN_SET);
				}
				else
				{
					HAL_GPIO_WritePin(LED_1_GPIO_Port, LED_1_Pin, GPIO_PIN_RESET);
				}
			}
		}
		else
		{
			HAL_GPIO_WritePin(LED_1_GPIO_Port, LED_1_Pin, GPIO_PIN_RESET);
			led_blink_period=200;
			if(led_blink_counter>led_blink_period)
				led_blink_counter=0;
			if(led_blink_counter>(led_blink_period/2))
			{
				HAL_GPIO_WritePin(LED_2_GPIO_Port, LED_2_Pin, GPIO_PIN_SET);
			}
			else
			{
				HAL_GPIO_WritePin(LED_2_GPIO_Port, LED_2_Pin, GPIO_PIN_RESET);
			}
			
		}
}

