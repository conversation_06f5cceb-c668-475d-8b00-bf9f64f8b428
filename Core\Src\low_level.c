/* Includes ------------------------------------------------------------------*/

#include <low_level.h>

#include <stdlib.h>
#include <math.h>
//#include <cmsis_os.h>

#include <main.h>
#include <adc.h>
#include <tim.h>
#include <utils.h>
#include "delay.h"


/* Private defines -----------------------------------------------------------*/

/* Private macros ------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
/* Global constant data ------------------------------------------------------*/
/* Global variables ----------------------------------------------------------*/
short vbus_voltage = 120,device_temperature=250; 
int ADCValue[4];
int Id,Iq,Iq_real,Id_real;
short phase_dir=1,phase_dir_B=1,hall_phase_dir=1,vel_dir=1;
int Iq_demand=0,Id_demand=0,target_Iq=0,target_Id=0;
int target_speed=0,speed_demand=0,target_position=0,target_position_b=0,position_demand;
int commutation_current=2000,motor_rated_current=2000,motor_peak_current=2000,motor_overload_time=1000;
short commutation_founded=0,commutation_mode=0,commutation_time=1000;
int loop_counter_c=0,loop_counter_v=1,loop_counter_p=2,current_loop_ready=0,velocity_loop_ready=0,position_loop_ready=0;
int phase_view;
uint16_t feedback_type=1,poles_num=2,motor_code=0;
int feedback_resolution=4000;
short over_voltage,under_voltage,chop_voltage,over_temperature;
short tamagawa_offset=0,tamagawa_dir=1;
short Driver_Ready=0;
int drv8301_error=0;
unsigned short ENC_Z_Count=0,ENC_Z_Count_B=0,ENC_Z_Count_C=0,ENC_Z_First=0,ENC_Z_Trig=0,ENC_Counting_Error=0;
short ENC_Z_Diff=0,ENC_Z_Diff_B=0;
int ENC_Z_Pos=0,ENC_Z_Pos_B=0,ENC_Z_Pos_Offset=0,ENC_Z_Pos_Diff=0,ENC_Z_Pos_Diff_B=0;

short hall_u=0,hall_v=0,hall_w=0,hall_state=0,hall_state_b=0,hall_error=0;
short hall_count=0,hall_get_position=0,start_calibrate_hall_phase=0;
int encoder_direction_temp=0,encoder_direction_temp_b=0;
short hall_phase[8],ENC_Z_Offset=2680,hall_phase_offset=0,ENC_Z_Phase=0,ENC_Z_Phase_B=0,ENC_Z_Phase_Err=0;
int hall_position=0,hall_position_b=0;
short encoder_offset_diff=0,hall_phase_offset_diff=0;
union error_uint32_t Error_State;
union can_int32_t Scop_Buffer[4][512]={0};
short Scop_Period=31,Scop_Period_counter=0,Scop_Buffer_point=0,Scop_Start=0,Scop_Data_Ready=0,Scop_Send_point=0;
short Scop_Point[4];

int led_blink_counter=0,led_blink_period=1000;

union can_int32_t Scop_Chanel[4];
unsigned char Scop_Send_Buf[20];

u16 store_parameter=0;

Motor_t motors[2] = {
    {   
        .motor_timer = &htim1,
        .next_timings = {TIM_1_8_PERIOD_CLOCKS/2, TIM_1_8_PERIOD_CLOCKS/2, TIM_1_8_PERIOD_CLOCKS/2},
        .control_deadline = TIM_1_8_PERIOD_CLOCKS,
        .current_meas = {0, 0},
        .DC_calib = {0, 0},
        
        .shunt_conductance = 5, //[S]
        .rotor = {
            //.encoder_timer = &htim3,
            .encoder_offset = 0,
            .encoder_state = 0,
            .phase = 0.0f, // [rad]
            .pll_pos = 0.0f, // [rad]
            .pll_vel = 0.0f, // [rad/s]
            .pll_kp = 0.0f, // [rad/s / rad]
            .pll_ki = 0.0f // [(rad/s^2) / rad]
        }
    }
};
const int num_motors = sizeof(motors)/sizeof(motors[0]);

/* Private constant data -----------------------------------------------------*/
static const int one_by_sqrt3 = 577;
static const int sqrt3_by_2 = 866;

static void start_adc_pwm(void);

/* Function implementations --------------------------------------------------*/

// Initalises the low level motor control and then starts the motor control threads
/* 
 * 函数名: init_motor_control
 * 功能描述: 初始化电机控制系统，包括ADC校准、启动编码器和参数检查
 * 参数: 无
 * 返回值: 无
 * 说明: 该函数执行以下步骤：
 *      1. 延时10ms等待系统稳定
 *      2. 执行ADC直流偏移校准
 *      3. 启动ADC采样
 *      4. 如果使用Tamagawa编码器，检查初始化状态
 *      5. 设置驱动器就绪标志
 */
void init_motor_control(void) 
{
    delay_ms(10);  // 延时10ms等待系统稳定
		//if(vbus_voltage<140)
		//	Error_State=Error_State|0x0010;
		
		//if(Error_State*0x10==0)
		My_DC_calib();  // 执行ADC直流偏移校准

    start_adc();  // 启动ADC采样
    // Start Encoders
    //HAL_TIM_Encoder_Start(&htim3, TIM_CHANNEL_ALL);
	
		Driver_Ready=1;  // 设置驱动器就绪标志
	
    delay_ms(50);  // 延时50ms等待编码器初始化完成
		if(feedback_type==4)  // 如果使用Tamagawa编码器
		{
			if(Tamagawa_First<10)  // 检查Tamagawa编码器是否成功初始化
			{
				Error_State.bits.ENC_error=1;  // 设置编码器错误标志
			}
			else
			{
				pos_offest=motors[0].rotor.encoder_state;  // 记录初始位置偏移量
			}
		}
    delay_ms(10);  // 最终延时10ms确保所有初始化完成
}

//@TODO make available from anywhere
/* 
 * 函数名: safe_assert
 * 功能描述: 安全断言函数，用于检查参数是否有效
 * 参数: arg - 断言条件，为0时触发保护
 * 返回值: 无
 * 说明: 如果条件不满足，会关闭PWM输出并进入死循环，确保电机安全停止
 */
void safe_assert(int arg) {
    if(!arg) {
        htim1.Instance->BDTR &= ~(TIM_BDTR_MOE);  // 禁用PWM主输出
        for(;;);  // 进入死循环
    }
}

/* 
 * 函数名: start_adc_pwm
 * 功能描述: 启动ADC采样和PWM输出
 * 参数: 无
 * 返回值: 无
 * 说明: 初始化ADC、启用中断并启动PWM输出
 */
static void start_adc_pwm(void){
    //Enable ADC and interrupts
    __HAL_ADC_ENABLE(&hadc1);  // 使能ADC1
    __HAL_ADC_ENABLE(&hadc2);  // 使能ADC2
    //__HAL_ADC_ENABLE(&hadc3);
    //Warp field stabilize.
    delay_ms(2);  // 延时2ms等待ADC稳定
    __HAL_ADC_ENABLE_IT(&hadc1, ADC_IT_JEOC);  // 使能ADC1注入通道转换完成中断
    __HAL_ADC_ENABLE_IT(&hadc2, ADC_IT_JEOC);  // 使能ADC2注入通道转换完成中断

    //Ensure that debug halting of the core doesn't leave the motor PWM running
    __HAL_DBGMCU_FREEZE_TIM1();  // 设置调试时TIM1冻结，防止调试时电机继续运行
    //__HAL_DBGMCU_FREEZE_TIM8();

    start_pwm(&htim1);  // 启动PWM输出
}

/* 
 * 函数名: start_adc
 * 功能描述: 启动ADC采样
 * 参数: 无
 * 返回值: 无
 * 说明: 初始化ADC、启用中断并启动TIM1的通道4作为触发源
 */
void start_adc(void){
    //Enable ADC and interrupts
    __HAL_ADC_ENABLE(&hadc1);  // 使能ADC1
    __HAL_ADC_ENABLE(&hadc2);  // 使能ADC2
    //Warp field stabilize.
    delay_ms(2);  // 延时2ms等待ADC稳定
    __HAL_ADC_ENABLE_IT(&hadc1, ADC_IT_JEOC);  // 使能ADC1注入通道转换完成中断
    __HAL_ADC_ENABLE_IT(&hadc2, ADC_IT_JEOC);  // 使能ADC2注入通道转换完成中断

    HAL_ADCEx_InjectedStart(&hadc1);  // 启动ADC1注入通道转换
    HAL_ADCEx_InjectedStart(&hadc2);  // 启动ADC2注入通道转换
	
    __HAL_DBGMCU_FREEZE_TIM1();  // 设置调试时TIM1冻结
    //__HAL_DBGMCU_FREEZE_TIM8();

    htim1.Instance->CCR4 = 1;  // 设置TIM1通道4的比较值为1
    HAL_TIM_PWM_Start_IT(&htim1, TIM_CHANNEL_4);  // 启动TIM1通道4的PWM输出并启用中断
}

/* 
 * 函数名: start_pwm
 * 功能描述: 启动三相PWM输出
 * 参数: htim - 定时器句柄指针
 * 返回值: 无
 * 说明: 设置三个通道的占空比并启动PWM输出（包括互补输出）
 */
void start_pwm(TIM_HandleTypeDef* htim){
    //Init PWM
    int half_load = TIM_1_8_PERIOD_CLOCKS/2;  // 计算50%占空比对应的值
    htim->Instance->CCR1 = half_load;  // 设置通道1的占空比为50%
    htim->Instance->CCR2 = half_load;  // 设置通道2的占空比为50%
    htim->Instance->CCR3 = half_load;  // 设置通道3的占空比为50%

    //This hardware obfustication layer really is getting on my nerves
    HAL_TIM_PWM_Start(htim, TIM_CHANNEL_1);  // 启动通道1 PWM正输出
    HAL_TIMEx_PWMN_Start(htim, TIM_CHANNEL_1);  // 启动通道1 PWM互补输出
    HAL_TIM_PWM_Start(htim, TIM_CHANNEL_2);  // 启动通道2 PWM正输出
    HAL_TIMEx_PWMN_Start(htim, TIM_CHANNEL_2);  // 启动通道2 PWM互补输出
    HAL_TIM_PWM_Start(htim, TIM_CHANNEL_3);  // 启动通道3 PWM正输出
    HAL_TIMEx_PWMN_Start(htim, TIM_CHANNEL_3);  // 启动通道3 PWM互补输出
}

/* 
 * 函数名: stop_pwm
 * 功能描述: 停止三相PWM输出
 * 参数: htim - 定时器句柄指针
 * 返回值: 无
 * 说明: 设置三个通道的占空比为50%并停止PWM输出
 */
void stop_pwm(TIM_HandleTypeDef* htim){
    //Init PWM
    int half_load = TIM_1_8_PERIOD_CLOCKS/2;  // 计算50%占空比对应的值
    htim->Instance->CCR1 = half_load;  // 设置通道1的占空比为50%
    htim->Instance->CCR2 = half_load;  // 设置通道2的占空比为50%
    htim->Instance->CCR3 = half_load;  // 设置通道3的占空比为50%

    //This hardware obfustication layer really is getting on my nerves
    HAL_TIM_PWM_Stop(htim, TIM_CHANNEL_1);  // 停止通道1 PWM正输出
    HAL_TIMEx_PWMN_Stop(htim, TIM_CHANNEL_1);  // 停止通道1 PWM互补输出
    HAL_TIM_PWM_Stop(htim, TIM_CHANNEL_2);  // 停止通道2 PWM正输出
    HAL_TIMEx_PWMN_Stop(htim, TIM_CHANNEL_2);  // 停止通道2 PWM互补输出
    HAL_TIM_PWM_Stop(htim, TIM_CHANNEL_3);  // 停止通道3 PWM正输出
    HAL_TIMEx_PWMN_Stop(htim, TIM_CHANNEL_3);  // 停止通道3 PWM互补输出
}

/* 
 * 函数名: phase_current_from_adcval
 * 功能描述: 将ADC值转换为相电流值
 * 参数: ADCValue - ADC采样值
 *       motornum - 电机编号
 * 返回值: 计算得到的电流值(mA)
 * 说明: 根据ADC采样值和分流电阻导通率计算实际电流
 */
int phase_current_from_adcval(uint32_t ADCValue, int motornum) {
    int amp_gain;

	amp_gain=10; //did not config the gain
    int adcval_bal = (int)ADCValue - (1<<11);
    int amp_out_volt = ONE_ADC_VOLTAGE * adcval_bal;
    int shunt_volt = amp_out_volt / amp_gain;
    int current = shunt_volt / motors[motornum].shunt_conductance; // unit mA
    return current;
}

/* 
 * 函数名: My_DC_calib
 * 功能描述: 执行电流传感器直流偏移校准
 * 参数: 无
 * 返回值: 无
 * 说明: 读取电流传感器的零点偏移，用于后续电流测量的校准
 */
void My_DC_calib(void)
{
    //Enable ADC and interrupts
    __HAL_ADC_ENABLE(&hadc1);  // 使能ADC1
    __HAL_ADC_ENABLE(&hadc2);  // 使能ADC2
    //Warp field stabilize.
    delay_ms(2);  // 延时2ms等待ADC稳定
    __HAL_ADC_ENABLE_IT(&hadc1, ADC_IT_JEOC);  // 使能ADC1注入通道转换完成中断
    __HAL_ADC_ENABLE_IT(&hadc2, ADC_IT_JEOC);  // 使能ADC2注入通道转换完成中断
	
		HAL_ADCEx_InjectedStart(&hadc1);  // 启动ADC1注入通道转换
		HAL_ADCEx_InjectedStart(&hadc2);  // 启动ADC2注入通道转换
	
    htim1.Instance->CCR4 = 1;  // 设置TIM1通道4的比较值为1
		OC4_PWM_Override(&htim1);  // 强制输出PWM，确保ADC触发
		HAL_TIM_PWM_Start_IT(&htim1, TIM_CHANNEL_4);  // 启动TIM1通道4的PWM输出并启用中断
    delay_ms(200);  // 延时200ms等待采样稳定
    motors[0].DC_calib.phB = phase_current_from_adcval(ADCValue[0],0);  // 记录B相电流零点偏移
    motors[0].DC_calib.phC = phase_current_from_adcval(ADCValue[1],0);  // 记录C相电流零点偏移
		if((motors[0].DC_calib.phB<-5000)||(motors[0].DC_calib.phB>5000))
			Error_State.bits.ADC_error=1;  // 设置ADC错误标志
		if((motors[0].DC_calib.phC<-5000)||(motors[0].DC_calib.phC>5000))
			Error_State.bits.ADC_error=1;  // 设置ADC错误标志
}

/* 
 * 函数名: queue_modulation_timings
 * 功能描述: 根据空间矢量调制计算PWM占空比并更新定时器值
 * 参数: motor - 电机结构体指针
 *       mod_alpha - Alpha轴调制量
 *       mod_beta - Beta轴调制量
 * 返回值: 无
 * 说明: 使用空间矢量调制(SVM)计算三相PWM占空比
 */
int tA, tB, tC;
void queue_modulation_timings(Motor_t* motor, int mod_alpha, int mod_beta) {
    
    SVM(mod_alpha, mod_beta, &tA, &tB, &tC);  // 调用SVM算法计算三相占空比
    motor->next_timings[0] = (tC * TIM_1_8_PERIOD_CLOCKS)/1000;  // 计算定时器C相比较值
    motor->next_timings[1] = (tA * TIM_1_8_PERIOD_CLOCKS)/1000;  // 计算定时器A相比较值
    motor->next_timings[2] = (tB * TIM_1_8_PERIOD_CLOCKS)/1000;  // 计算定时器B相比较值
}

/* 
 * 函数名: get_electric_phase
 * 功能描述: 获取指定电流下的电气角度
 * 参数: commutation_current - 通电电流大小(mA)
 * 返回值: 获取到的电气角度
 * 说明: 用于电机初始定位和换相角度校准
 */
int32_t get_electric_phase(int commutation_current)
{
	int32_t phase_offset,current_step,i;

	Iq_demand=commutation_current;
	delay_ms(commutation_time);
	phase_offset = motors[0].rotor.encoder_state;
	delay_ms(commutation_time);
	return phase_offset;
}
int my_p0,my_p1,my_dir;
/* 
 * 函数名: find_commutation
 * 功能描述: 查找并确定电机的换相角度
 * 参数: 无
 * 返回值: 无
 * 说明: 根据不同的反馈类型和换相模式，确定电机的相位方向和编码器偏移
 */
void find_commutation(void)
{
	switch(commutation_mode)
	{
		case 0:
			switch(feedback_type)
			{
				case 1:	
				case 4:		
				case 8:		
					phase_dir=1;    // must set back to the default value
					start_pwm(&htim1);
					motor_on=1;
					motors[0].rotor.phase=0;
					my_p0=get_electric_phase(commutation_current);
					motors[0].rotor.phase=M_PI/2;
					my_p1=get_electric_phase(commutation_current);
					motors[0].rotor.phase=0;
					my_p0=get_electric_phase(commutation_current);
				
					Iq_demand=0;
				
					if(my_p1>=my_p0)
					{
						phase_dir=1;
						if((feedback_resolution/(5*poles_num))<(my_p1-my_p0)&&(my_p1-my_p0)<(feedback_resolution/(3*poles_num)))
						{
							commutation_founded=1;
							motors[0].rotor.encoder_state=0;
							motors[0].rotor.encoder_offset=my_p0%feedback_resolution;
						}
					}
					else
					{
						phase_dir=-1;
						if((feedback_resolution/(5*poles_num))<(my_p0-my_p1)&&(my_p1-my_p0)<(feedback_resolution/(3*poles_num)))
						{
							commutation_founded=1;
							motors[0].rotor.encoder_state=0;
							motors[0].rotor.encoder_offset=feedback_resolution-my_p0%feedback_resolution;
						}
					}
					if(feedback_type==4)
					{
						if(commutation_founded==1)
						{
							tamagawa_dir=phase_dir;
							tamagawa_offset=motors[0].rotor.encoder_offset;
						}
					}
					if(commutation_founded==0)
					{
						stop_pwm(&htim1);
				 		motor_on=0;
					}
					break;
				default:
					break;
			}		
			break;
		case 1:
		case 2:
			switch(feedback_type)
			{
				case 1:		
					if(hall_state==0||hall_state==7)
					{
						Error_State.bits.hall_state_error=1;
					}
					if(hall_phase[5]>hall_phase[4])
					{
						hall_phase_dir=-1;
					}
					else
					{
						hall_phase_dir=1;
					}
					motors[0].rotor.encoder_state=0;
					motors[0].rotor.encoder_timer->Instance->CNT=0;
					if(phase_dir==1)
						encoder_offset_diff = (hall_phase[hall_state]+hall_phase_offset)*feedback_resolution/(M_PI*poles_num*2);
					else
						encoder_offset_diff = -feedback_resolution + (hall_phase[hall_state]+hall_phase_offset)*feedback_resolution/(M_PI*poles_num*2);
					
					motors[0].rotor.encoder_offset=-encoder_offset_diff;
				
					motors[0].rotor.encoder_state=0;
					ENC_Z_Count=0;
					ENC_Z_Count_B=0;
					ENC_Z_First=0;
					commutation_founded=1;	
					break;
				case 4:
					phase_dir=tamagawa_dir;
					motors[0].rotor.encoder_state=tamagawa_angle;
					motors[0].rotor.encoder_offset=tamagawa_offset;
					commutation_founded=1;
					break;
				case 8:
					
					break;
				default:
					break;
			}		
			break;
		default:
			break;
	}
	
		//motors[0].rotor.encoder_offset=get_electric_phase(commutation_current)+feedback_resolution/(4*poles_num);
		
}
int rad_of_round= 2*M_PI*2;
/* 
 * 函数名: update_rotor
 * 功能描述: 更新转子位置和相位信息
 * 参数: rotor - 转子结构体指针
 * 返回值: 无
 * 说明: 根据不同的反馈类型更新转子位置，计算电气角度，处理霍尔传感器信号
 */
void update_rotor(Rotor_t* rotor) {
		int16_t delta_enc;
    //@TODO stick parameter into struct
		rad_of_round= 2*M_PI*poles_num;
    //update internal encoder state
    //int16_t delta_enc = (int16_t)rotor->encoder_timer->Instance->CNT - (int16_t)rotor->encoder_state;
		//rotor->encoder_state += (int32_t)delta_enc;
		switch(feedback_type)
		{
			case 0:
			case 1:
			case 2:
				delta_enc = (int16_t)rotor->encoder_timer->Instance->CNT - (int16_t)rotor->encoder_state;
				rotor->encoder_state += (int32_t)delta_enc;
			break;
			case 4:
				delta_enc = tamagawa_angle - tamagawa_angle_b;
				if(delta_enc<(-feedback_resolution/2))
					delta_enc+=feedback_resolution;
				if(delta_enc>(feedback_resolution/2))
					delta_enc-=feedback_resolution;
				tamagawa_angle_b=tamagawa_angle;
				rotor->encoder_state += (int32_t)delta_enc;
				break;
			case 8:
		
				break;
			default:
				break;
		}
	
		hall_u=HAL_GPIO_ReadPin(HALL_U_GPIO_Port,HALL_U_Pin);
		hall_v=HAL_GPIO_ReadPin(HALL_V_GPIO_Port,HALL_V_Pin);
		hall_w=HAL_GPIO_ReadPin(HALL_W_GPIO_Port,HALL_W_Pin);
	
		hall_state_b=hall_state;
		hall_state=hall_u+(hall_v<<1)+(hall_w<<2);
		int ph;
    //compute electrical phase
		if(phase_dir==1)
			ph= M_PI/2 + (rad_of_round * ((rotor->encoder_state % feedback_resolution) - rotor->encoder_offset))/feedback_resolution;
		else
			ph= M_PI/2 + (rad_of_round * ((feedback_resolution-(rotor->encoder_state % feedback_resolution)) - rotor->encoder_offset))/feedback_resolution;
		
		ph = ph%(2*M_PI);
		//if(ph<0)
		//	ph+=2*M_PI;

		switch(operation_mode)
		{  
			case 1:
			case 3:
			case 4:
			case 2:
			case 5:
			case 7:			
				if(commutation_founded)
					rotor->phase = ph;
				break;
			case 14:
			case 13:
			case 12:
			case 11:
			case 17:
				// hall 
				if(hall_phase[hall_state]>hall_phase[hall_state_b])
				{
					if((hall_phase[hall_state]-hall_phase[hall_state_b])<M_PI)
					{
						hall_position+=1;
						//ph=hall_phase[hall_state]-hall_phase_offset;
					}
					else
					{
						hall_position-=1;
						//ph=hall_phase[hall_state_b]-hall_phase_offset;
					}
				}
				else if(hall_phase[hall_state]<hall_phase[hall_state_b])
				{
					if((hall_phase[hall_state_b]-hall_phase[hall_state])<M_PI)
					{
						hall_position-=1;
						//ph=hall_phase[hall_state_b]-hall_phase_offset;
					}
					else
					{
						hall_position+=1;
						//ph=hall_phase[hall_state]-hall_phase_offset;
					}
				}
				if(speed_demand>0)
					ph=hall_phase[hall_state]+hall_phase_offset+862;
				else
					ph=hall_phase[hall_state]+hall_phase_offset+862;
				ph = ph%(2*M_PI);
				if(ph<0)
					ph+=2*M_PI;
				rotor->phase=ph;
				break;
			case -8:
				if(hall_get_position)
				{
					get_hall_edge_phase();
				}
				break;
			default:
				break; 
			
			}

		/*
		Align the electrical angle by hall_u edge.
		*/
			
			if((commutation_founded==1)&&(commutation_mode==2))
			{
				//ENC_Z_First=1;
				if((hall_state==4)&&(hall_state_b==5))
				{
					if(hall_phase_dir==1)		
						hall_phase_offset_diff=((hall_phase[hall_state]+M_PI/2)-motors[0].rotor.phase);
					else
						hall_phase_offset_diff=((hall_phase[hall_state_b]+M_PI/2)-motors[0].rotor.phase);
					
					hall_phase_offset_diff=hall_phase_offset_diff%(2*M_PI);
					encoder_offset_diff=hall_phase_offset_diff*feedback_resolution/(M_PI*poles_num*2);
					if((commutation_founded==1)&&(commutation_mode==2))
						if(ENC_Z_First==0)
						{
							motors[0].rotor.encoder_offset-=encoder_offset_diff;
							ENC_Z_First=1;
						}
				}
				if((hall_state==5)&&(hall_state_b==4))
				{
					if(hall_phase_dir==1)		
						hall_phase_offset_diff=((hall_phase[hall_state_b]+M_PI/2)-motors[0].rotor.phase);
					else
						hall_phase_offset_diff=((hall_phase[hall_state]+M_PI/2)-motors[0].rotor.phase);
					
					hall_phase_offset_diff=hall_phase_offset_diff%(2*M_PI);
					encoder_offset_diff=hall_phase_offset_diff*feedback_resolution/(M_PI*poles_num*2);
					if((commutation_founded==1)&&(commutation_mode==2))
						if(ENC_Z_First==0)
						{
							motors[0].rotor.encoder_offset-=encoder_offset_diff;
							ENC_Z_First=1;
						}
				}
		}

}

/* 
 * 函数名: get_hall_edge_phase
 * 功能描述: 获取霍尔传感器边沿对应的相位
 * 参数: 无
 * 返回值: 无
 * 说明: 用于校准霍尔传感器的相位角度，确定电机旋转方向
 */
void get_hall_edge_phase(void)
{
	if(hall_get_position==1) // 初始化并开始获取霍尔信号和Z信号
	{
			motors[0].rotor.encoder_state=0;
			motors[0].rotor.encoder_timer->Instance->CNT=0;
			ENC_Z_Count=0;
			ENC_Z_Count_B=0;
			ENC_Z_First=0;
			ENC_Z_Phase=6800; // 设置初始值大于6280
			hall_count=0;
			hall_get_position=2;
	}
	if(hall_state!=hall_state_b) // 霍尔状态变化时
	{
		hall_phase[hall_state]=motors[0].rotor.phase; // 记录当前相位
		hall_count++;
		encoder_direction_temp_b=encoder_direction_temp;
		encoder_direction_temp=motors[0].rotor.encoder_state;
	}
	if(hall_get_position==2) // 检查编码器方向
	{
		if(hall_count>3) 
		{
			if(encoder_direction_temp>encoder_direction_temp_b) // 正方向旋转
			{
				phase_dir=1;
				hall_get_position=3;
			}
			else // 负方向旋转
			{
				phase_dir=-1;
				hall_get_position=1; // 重新开始获取
			}
			
		}
	}
	if(hall_get_position==3) // 等待足够的霍尔状态变化
	{
		if(hall_count>18)
		{
			hall_get_position=4;
			ENC_Z_Phase=6800;
		}
	}
	if(hall_get_position==4) // 完成相位获取
	{
		if(ENC_Z_Phase!=6800)
		{
			ENC_Z_Phase_B=ENC_Z_Phase;
			hall_get_position=0; // 完成校准过程
		}
	}
}

/* 
 * 函数名: Process_Scop_Data
 * 功能描述: 处理示波器数据采集
 * 参数: 无
 * 返回值: 无
 * 说明: 按设定周期采集各种电机状态数据，存入缓冲区供后续发送
 */
void Process_Scop_Data(void)
{
	int i,j;
	Scop_Period_counter++;
	if(Scop_Buffer_point<512) // 缓冲区未满
	{
		if(Scop_Period_counter>Scop_Period) // 达到采样周期
		{
			// 将当前数据存入示波器通道
			Scop_Chanel[0].all=real_speed_filter;
			Scop_Chanel[1].all=pos_actual;
			Scop_Chanel[2].all=real_speed_filter;
			Scop_Chanel[3].all=real_speed_filter;
			
			// 将所需监控的数据存入缓冲区
			Scop_Buffer[0][Scop_Buffer_point].all=Driver_IIt_Real; 
			Scop_Buffer[1][Scop_Buffer_point].all=Driver_IIt_Real_DC;
			Scop_Buffer[2][Scop_Buffer_point].all=device_temperature*100;
			Scop_Buffer[3][Scop_Buffer_point].all=Iq_real;
			Scop_Period_counter=0; // 重置计数器
			Scop_Buffer_point++; // 缓冲区指针递增
		}
	}
	else // 缓冲区已满
	{
		Scop_Buffer_point=0; // 重置缓冲区指针
		Scop_Data_Ready=1; // 标记数据准备就绪
		Scop_Start=0; // 停止采集
	}
}

/* 
 * 函数名: Send_Scop_Data
 * 功能描述: 发送示波器数据
 * 参数: 无
 * 返回值: 无
 * 说明: 将示波器缓冲区中的数据通过通信接口发送出去
 */
void Send_Scop_Data(void)
{
	int i;
	
	//if((Scop_Data_Ready==0)&&(Scop_Start==0))
		//if(auto_reverse_status==1)
			//Scop_Start=1;
		
	if(Scop_Data_Ready) // 数据准备就绪
	{
		Scop_Send_point++; // 发送指针递增
		if(Scop_Send_point<512) // 未发送完所有数据
		{
			// 构造数据包帧头
			Scop_Send_Buf[0]=0x03;
			Scop_Send_Buf[1]=0xfc;
			
			// 打包四个通道的数据
			for(i=0;i<4;i++)
			{
				Scop_Send_Buf[2+i*4]=Scop_Buffer[i][Scop_Send_point].byte.byte_0;
				Scop_Send_Buf[3+i*4]=Scop_Buffer[i][Scop_Send_point].byte.byte_1;
				Scop_Send_Buf[4+i*4]=Scop_Buffer[i][Scop_Send_point].byte.byte_2;
				Scop_Send_Buf[5+i*4]=Scop_Buffer[i][Scop_Send_point].byte.byte_3;
			}
			
			// 构造数据包帧尾
			Scop_Send_Buf[18]=0xfc;
			Scop_Send_Buf[19]=0x03;
					
			// 发送数据包
			Modbus_Solve_PutString(Scop_Send_Buf,20);
		}
		else // 所有数据已发送完
		{
			Scop_Data_Ready=0; // 清除就绪标志
			Scop_Send_point=0; // 重置发送指针
		}
	}
}

/* 
 * 函数名: calibrate_hall_phase
 * 功能描述: 校准霍尔传感器相位
 * 参数: 无
 * 返回值: 无
 * 说明: 通过控制电机低速旋转，测量霍尔传感器各状态对应的相位角度
 */
void calibrate_hall_phase(void)
{
	if(start_calibrate_hall_phase==1) // 开始校准
	{
		// 初始化参数
		auto_reverse_p_time=0;
		auto_reverse_n_time=0;
		target_Iq=commutation_current; // 设置测试电流
		target_speed=1; // 设置低速旋转
		operation_mode=-8; // 设置为特殊校准模式
		control_word.all=0x0f; // 使能驱动
		delay_ms(1000); // 延时等待电机稳定
		hall_get_position=1; // 启动霍尔相位获取流程
		start_calibrate_hall_phase=2; // 进入下一步
	}
	if(start_calibrate_hall_phase==2) // 等待校准完成
	{
		if(hall_get_position==0) // 霍尔相位获取完成
		{
			// 停止电机
			target_Iq=0;
			target_speed=0;
			operation_mode=-8;
			control_word.all=0x06; // 禁用驱动
			start_calibrate_hall_phase=3; // 校准完成
		}
	}
}

/* 
 * 函数名: calibrate_tamagawa_encoder
 * 功能描述: 校准Tamagawa编码器零点
 * 参数: 无
 * 返回值: 无
 * 说明: 控制电机定位到零点位置，记录Tamagawa编码器的零点偏移
 */
void calibrate_tamagawa_encoder(void)
{
	if(set_tamagawa_zero==2) // 开始校准
	{
		// 初始化参数
		auto_reverse_p_time=0;
		auto_reverse_n_time=0;
		target_Iq=commutation_current; // 设置测试电流
		target_speed=0; // 保持静止
		phase_dir=1; // 设置相位方向
		operation_mode=-8; // 设置为特殊校准模式
		control_word.all=0x0f; // 使能驱动
		delay_ms(1000); // 延时等待电机稳定
		set_tamagawa_zero=1; // 记录零点
		delay_ms(1000); // 延时确保稳定
		set_tamagawa_zero=3; // 进入下一步
	}
	if(set_tamagawa_zero==3) // 完成校准
	{
		// 停止电机
		target_Iq=0;
		target_speed=0;
		operation_mode=-8;
		control_word.all=0x06; // 禁用驱动
		set_tamagawa_zero=0; // 校准完成
	}
}

