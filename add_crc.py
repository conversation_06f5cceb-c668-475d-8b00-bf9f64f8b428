import os
import struct
import time
from datetime import datetime

def crc32_custom(data, poly=0x04c11db7, init_crc=0x00000000, ref_in=False, ref_out=False, xor_out=0x00000000):
    """
    自定义CRC32计算函数
    poly: 多项式值 (0x04c11db7)
    init_crc: 初始值 (0x00000000)
    ref_in: 输入反转 (False)
    ref_out: 输出反转 (False)
    xor_out: 输出异或值 (0x00000000)
    """
    
    def reflect_byte(byte_val):
        """反转字节的位顺序"""
        result = 0
        for i in range(8):
            if byte_val & (1 << i):
                result |= (1 << (7 - i))
        return result
    
    def reflect_32(val):
        """反转32位数据的位顺序"""
        result = 0
        for i in range(32):
            if val & (1 << i):
                result |= (1 << (31 - i))
        return result
    
    crc = init_crc
    
    for byte in data:
        if ref_in:
            byte = reflect_byte(byte)
        
        crc ^= (byte << 24)
        
        for _ in range(8):
            if crc & 0x80000000:
                crc = ((crc << 1) ^ poly) & 0xFFFFFFFF
            else:
                crc = (crc << 1) & 0xFFFFFFFF
    
    if ref_out:
        crc = reflect_32(crc)
    
    return crc ^ xor_out

def calculate_firmware_crc32(file_path):
    """计算固件文件的CRC32值"""
    try:
        with open(file_path, 'rb') as file:
            file_content = file.read()
            # 使用自定义参数：多项式0x04c11db7，初值0，不反转，不异或
            crc32_value = crc32_custom(
                data=file_content,
                poly=0x04c11db7,
                init_crc=0x00000000,
                ref_in=False,
                ref_out=False,
                xor_out=0x00000000
            )
            return crc32_value
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return None

def save_crc32_to_file(crc32_value, firmware_file, output_file):
    """将CRC32结果保存到txt文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            file.write(f"0x{crc32_value:08X}\n")
        print(f"CRC32结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件时发生错误：{e}")

class FirmwareHeader:
    """固件头部结构 - 简化版 20字节"""
    def __init__(self):
        self.version = 0x0001     # 版本号 (4字节)
        self.firmware_size = 0    # 实际固件大小（不包含头部）(4字节)
        self.firmware_crc = 0     # 固件CRC32 (4字节)
        self.reserved1 = 0        # 预留字节1 (4字节)
        self.reserved2 = 0        # 预留字节2 (4字节)

class FirmwareTail:
    """固件尾部结构 - 16字节"""
    def __init__(self, version=0x0001):
        self.version = version              # 版本号 (4字节)
        self.firmware_size = 0              # 实际固件大小（不包含尾部）(4字节)
        self.firmware_crc = 0               # 固件CRC32 (4字节)
        self.magic = 0x12345678            # 魔术字节，用于验证结构体有效性 (4字节)
    
    def pack(self):
        """打包尾部为字节数据"""
        # 尾部结构：16字节
        # Version(4) + FwSize(4) + FwCRC(4) + Magic(4)
        
        tail_data = struct.pack('<IIII', 
                               self.version, 
                               self.firmware_size, 
                               self.firmware_crc, 
                               self.magic)
        
        return tail_data
    
    def unpack(self, data):
        """从字节数据解包尾部"""
        if len(data) < 16:
            raise ValueError("数据长度不足以解包固件尾部")
        
        unpacked = struct.unpack('<IIII', data[-16:])
        self.version = unpacked[0]
        self.firmware_size = unpacked[1]
        self.firmware_crc = unpacked[2]
        self.magic = unpacked[3]
        
        return self
    
    def is_valid(self):
        """检查魔术字节是否有效"""
        return self.magic == 0x12345678
    
    def print_info(self):
        """打印尾部信息"""
        print(f"=== 固件尾部信息 ===")
        print(f"版本号: 0x{self.version:04X} (V{self.version >> 8}.{self.version & 0xFF})")
        print(f"固件大小: {self.firmware_size} 字节")
        print(f"固件CRC32: 0x{self.firmware_crc:08X}")
        print(f"魔术字节: 0x{self.magic:08X} {'[有效]' if self.is_valid() else '[无效]'}")


def append_firmware_tail(firmware_file, version=0x0001):
    """在固件尾部添加版本号、固件大小和CRC校验信息"""
    try:
        # 生成输出文件名
        base_name = os.path.splitext(firmware_file)[0]
        output_file = f"{base_name}_with_tail.bin"
        
        # 读取原始固件内容
        with open(firmware_file, 'rb') as f:
            firmware_data = f.read()
        
        print(f"原始固件大小: {len(firmware_data)} 字节")
        
        # 计算固件CRC32
        firmware_crc = crc32_custom(
            data=firmware_data,
            poly=0x04c11db7,
            init_crc=0x00000000,
            ref_in=False,
            ref_out=False,
            xor_out=0x00000000
        )
        
        # 创建固件尾部结构
        tail = FirmwareTail(version)
        tail.firmware_size = len(firmware_data)
        tail.firmware_crc = firmware_crc
        
        # 打包尾部数据
        tail_data = tail.pack()
        
        print(f"CRC32值: 0x{firmware_crc:08X}")
        print(f"版本号: 0x{version:04X} (V{version >> 8}.{version & 0xFF})")
        print(f"尾部数据 (小端): {' '.join(f'0x{b:02X}' for b in tail_data)}")
        
        # 将尾部信息追加到固件末尾
        firmware_with_tail = firmware_data + tail_data
        
        # 写入新的固件文件
        with open(output_file, 'wb') as f:
            f.write(firmware_with_tail)
            
        print(f"[OK] 固件尾部信息已添加")
        print(f"[OK] 新固件文件: {output_file}")
        print(f"[OK] 新固件大小: {len(firmware_with_tail)} 字节 (+16字节尾部信息)")
        
        # 打印尾部信息
        tail.print_info()
        
        return output_file, tail
        
    except Exception as e:
        print(f"添加固件尾部时发生错误: {e}")
        return None, None

def append_crc_to_firmware(firmware_file, crc32_value):
    """将CRC32值追加到固件文件末尾，生成带CRC的新固件（保持向后兼容）"""
    try:
        # 生成输出文件名
        base_name = os.path.splitext(firmware_file)[0]
        output_file = f"{base_name}_with_CRC.bin"
        
        # 读取原始固件内容
        with open(firmware_file, 'rb') as f:
            firmware_data = f.read()
        
        print(f"原始固件大小: {len(firmware_data)} 字节")
        
        # 将CRC32转换为4字节小端格式 (STM32使用小端架构)
        crc_bytes = crc32_value.to_bytes(4, byteorder='little')
        print(f"CRC32值: 0x{crc32_value:08X}")
        print(f"CRC32字节序 (小端): {' '.join(f'0x{b:02X}' for b in crc_bytes)}")
        
        # 将CRC追加到固件末尾
        firmware_with_crc = firmware_data + crc_bytes
        
        # 写入新的固件文件
        with open(output_file, 'wb') as f:
            f.write(firmware_with_crc)
            
        print(f"[OK] CRC已追加到固件末尾")
        print(f"[OK] 新固件文件: {output_file}")
        print(f"[OK] 新固件大小: {len(firmware_with_crc)} 字节 (+4字节CRC)")
        
        return output_file
        
    except Exception as e:
        print(f"追加CRC时发生错误: {e}")
        return None

def verify_firmware_tail(firmware_file_with_tail):
    """验证固件尾部信息是否正确"""
    try:
        with open(firmware_file_with_tail, 'rb') as f:
            firmware_data = f.read()
        
        if len(firmware_data) < 16:
            print("错误：固件文件太小，无法包含完整的尾部信息")
            return False
        
        # 解析尾部信息
        tail = FirmwareTail()
        tail.unpack(firmware_data)
        
        # 验证魔术字节
        if not tail.is_valid():
            print("错误：固件尾部魔术字节无效，可能不是带尾部信息的固件")
            return False
        
        # 分离固件数据和尾部
        app_data = firmware_data[:-16]  # 除了最后16字节的所有数据
        
        # 验证固件大小
        if len(app_data) != tail.firmware_size:
            print(f"错误：固件大小不匹配，期望{tail.firmware_size}字节，实际{len(app_data)}字节")
            return False
        
        # 重新计算应用程序部分的CRC
        calculated_crc = crc32_custom(
            data=app_data,
            poly=0x04c11db7,
            init_crc=0x00000000,
            ref_in=False,
            ref_out=False,
            xor_out=0x00000000
        )
        
        print(f"\n=== 固件尾部验证 ===")
        print(f"应用程序大小: {len(app_data)} 字节")
        print(f"存储的CRC: 0x{tail.firmware_crc:08X}")
        print(f"计算的CRC: 0x{calculated_crc:08X}")
        
        # 打印尾部信息
        tail.print_info()
        
        if tail.firmware_crc == calculated_crc:
            print("[OK] 固件尾部验证通过！")
            return True
        else:
            print("[FAIL] 固件尾部CRC验证失败！")
            return False
            
    except Exception as e:
        print(f"验证固件尾部时发生错误: {e}")
        return False

def verify_crc_in_firmware(firmware_file_with_crc):
    """验证固件中的CRC是否正确（保持向后兼容）"""
    try:
        with open(firmware_file_with_crc, 'rb') as f:
            firmware_data = f.read()
        
        if len(firmware_data) < 4:
            print("错误：固件文件太小，无法包含CRC")
            return False
        
        # 分离固件数据和CRC
        app_data = firmware_data[:-4]  # 除了最后4字节的所有数据
        stored_crc_bytes = firmware_data[-4:]  # 最后4字节
        
        # 从字节转换回CRC值 (小端格式)
        stored_crc = int.from_bytes(stored_crc_bytes, byteorder='little')
        
        # 重新计算应用程序部分的CRC
        calculated_crc = crc32_custom(
            data=app_data,
            poly=0x04c11db7,
            init_crc=0x00000000,
            ref_in=False,
            ref_out=False,
            xor_out=0x00000000
        )
        
        print(f"\n=== CRC验证 ===")
        print(f"应用程序大小: {len(app_data)} 字节")
        print(f"存储的CRC: 0x{stored_crc:08X}")
        print(f"计算的CRC: 0x{calculated_crc:08X}")
        
        if stored_crc == calculated_crc:
            print("[OK] CRC验证通过！")
            return True
        else:
            print("[FAIL] CRC验证失败！")
            return False
            
    except Exception as e:
        print(f"验证CRC时发生错误: {e}")
        return False

def create_firmware_with_header(firmware_file, output_file):
    """创建带头部的固件文件"""
    try:
        # 读取原始固件
        with open(firmware_file, 'rb') as f:
            firmware_data = f.read()
        
        print(f"原始固件大小: {len(firmware_data)} 字节")
        
        # 计算固件CRC
        firmware_crc = crc32_custom(firmware_data)
        print(f"固件CRC32: 0x{firmware_crc:08X}")
        
        # 创建固件头部
        header = FirmwareHeader()
        header.firmware_size = len(firmware_data)
        header.firmware_crc = firmware_crc
        
        # 打包头部
        header_data = header.pack()
        print(f"头部大小: {len(header_data)} 字节")
        
        # 组合完整固件：头部 + 原始固件
        complete_firmware = header_data + firmware_data
        
        # 写入新文件
        with open(output_file, 'wb') as f:
            f.write(complete_firmware)
        
        print(f"[OK] 带头部的固件已生成: {output_file}")
        print(f"[OK] 总大小: {len(complete_firmware)} 字节 (头部{len(header_data)}字节 + 固件{len(firmware_data)}字节)")
        
        # 打印头部信息
        header.print_info()
        
        return output_file, header
        
    except Exception as e:
        print(f"创建带头部固件时发生错误: {e}")
        return None, None

def verify_firmware_with_header(firmware_file):
    """验证带头部的固件"""
    try:
        with open(firmware_file, 'rb') as f:
            firmware_data = f.read()
        
        if len(firmware_data) < 20:
            print("错误：文件太小，不包含有效头部")
            return False
        
        # 解析头部 (20字节)
        unpacked = struct.unpack('<IIIII', firmware_data[:20])
        
        version = unpacked[0]
        firmware_size = unpacked[1]
        firmware_crc = unpacked[2]
        reserved1 = unpacked[3]
        reserved2 = unpacked[4]
        
        # 检查固件大小
        expected_total_size = 20 + firmware_size
        if len(firmware_data) != expected_total_size:
            print(f"错误：文件大小不匹配，期望{expected_total_size}字节，实际{len(firmware_data)}字节")
            return False
        
        # 验证固件CRC
        firmware_part = firmware_data[20:]  # 跳过头部
        calculated_firmware_crc = crc32_custom(firmware_part)
        if firmware_crc != calculated_firmware_crc:
            print(f"错误：固件CRC验证失败，存储0x{firmware_crc:08X}，计算0x{calculated_firmware_crc:08X}")
            return False
        
        print("\n=== 验证结果 ===")
        print("[OK] 固件CRC验证通过")
        print("[OK] 文件大小验证通过")
        
        return True
        
    except Exception as e:
        print(f"验证固件时发生错误: {e}")
        return False


def main():
    # 检查当前目录是否有APP_HAL.bin文件，如果没有则尝试上级目录
    firmware_file = "APP_HAL.bin"
    if not os.path.exists(firmware_file):
        firmware_file = "../APP_HAL.bin"
    
    output_file = "crc32.txt" if os.path.exists("APP_HAL.bin") else "../crc32.txt"
    
    # 检查固件文件是否存在
    if not os.path.exists(firmware_file):
        print(f"错误：找不到固件文件 APP_HAL.bin")
        print("请确保APP_HAL.bin文件在当前目录或上级目录中")
        return
    
    print(f"正在处理固件文件: {firmware_file}")
    print("CRC32参数：多项式=0x04C11DB7，初值=0x00000000，不反转，不异或")
    print("=" * 60)
    
    # 计算CRC32
    crc32_value = calculate_firmware_crc32(firmware_file)
    
    if crc32_value is not None:
        print(f"CRC32: 0x{crc32_value:08X}")
        print(f"CRC32 (十进制): {crc32_value}")
        
        # 保存结果到文件 (保持原有功能)
        save_crc32_to_file(crc32_value, firmware_file, output_file)
        
        print("\n" + "=" * 60)
        print("正在生成带固件尾部信息的固件...")
        
        # 生成在固件尾部添加版本号、固件大小和CRC的固件文件
        version = 0x0102  # 版本号 V1.2
        result_file, tail_info = append_firmware_tail(firmware_file, version)
        
        if result_file:
            print("\n" + "=" * 60)
            print("正在验证固件尾部信息...")
            
            # 验证固件尾部信息
            if verify_firmware_tail(result_file):
                print("\n" + "=" * 60)
                print("正在生成传统CRC校验固件（向后兼容）...")
                
                # 生成传统的CRC校验固件（保持向后兼容）
                crc_file = append_crc_to_firmware(firmware_file, crc32_value)
                
                if crc_file and verify_crc_in_firmware(crc_file):
                    print("\n" + "=" * 60)
                    print("所有处理完成！")
                    print(f"原始固件: {firmware_file}")
                    print(f"带尾部信息固件: {result_file}")
                    print(f"传统CRC校验固件: {crc_file}")
                    print(f"CRC信息文件: {output_file}")
                    
                    # 显示固件大小对比
                    original_size = os.path.getsize(firmware_file)
                    tail_size = os.path.getsize(result_file)
                    crc_size = os.path.getsize(crc_file)
                    print(f"\n固件大小对比:")
                    print(f"原始固件: {original_size} 字节")
                    print(f"带尾部信息固件: {tail_size} 字节 (尾部增加{tail_size - original_size}字节)")
                    print(f"传统CRC校验固件: {crc_size} 字节 (尾部增加{crc_size - original_size}字节)")
                    
                    print(f"\n推荐使用: {result_file} (包含完整的版本、大小和CRC信息)")
                else:
                    print("传统CRC校验固件生成或验证失败！")
            else:
                print("固件尾部信息验证失败！")
        else:
            print("生成带尾部信息固件失败！")
    else:
        print("CRC32计算失败")

if __name__ == "__main__":
    main()