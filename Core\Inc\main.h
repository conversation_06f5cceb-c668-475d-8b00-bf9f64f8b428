/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2020 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f1xx_hal.h"
#include "vofa_function.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define TIM_1_8_PERIOD_CLOCKS 1800
#define TIM_1_8_RCR 0
#define TIM_1_8_DEADTIME_CLOCKS 20
#define LED_1_Pin GPIO_PIN_13
#define LED_1_GPIO_Port GPIOC
#define LED_2_Pin GPIO_PIN_14
#define LED_2_GPIO_Port GPIOC
#define BRAKE_Pin GPIO_PIN_15
#define BRAKE_GPIO_Port GPIOC
#define Temp_Pin GPIO_PIN_0
#define Temp_GPIO_Port GPIOA
#define VBUS_Pin GPIO_PIN_1
#define VBUS_GPIO_Port GPIOA
#define SPI1_CS_Pin GPIO_PIN_4
#define SPI1_CS_GPIO_Port GPIOA
#define RS485_EN_Pin GPIO_PIN_2
#define RS485_EN_GPIO_Port GPIOB
#define TAMAGAWA_TX_EN_Pin GPIO_PIN_15
#define TAMAGAWA_TX_EN_GPIO_Port GPIOA
#define HALL_W_Pin GPIO_PIN_3
#define HALL_W_GPIO_Port GPIOB
#define HALL_V_Pin GPIO_PIN_4
#define HALL_V_GPIO_Port GPIOB
#define HALL_U_Pin GPIO_PIN_5
#define HALL_U_GPIO_Port GPIOB
#define ERR_Pin GPIO_PIN_8
#define ERR_GPIO_Port GPIOB
/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
