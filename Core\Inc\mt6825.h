
#ifndef _MT6825_H_
#define _MT6825_H_

#include "main.h"
#include "sys.h"

#define REFERESH_ANGLE		0

extern uint32_t MT_angle;

void SPI_SendData16(uint16_t SendData);
uint16_t SPI_ReadData16(void);
uint32_t ReadValue(uint32_t u32Value);
uint16_t SPIx_ReadWriteByte(uint16_t byte);

// 位置转换函数
int32_t encoder_to_angle_degree_x100(int32_t encoder_pos);    // 编码器值转角度（度×100）
int16_t encoder_to_angle_degree(int32_t encoder_pos);  // 编码器值转角度

#endif
