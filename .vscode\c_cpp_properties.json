{"configurations": [{"name": "windows-gcc-x64", "compilerPath": "E:/tool/mingw64/bin/gcc.exe", "compileCommands": ["${config:idf.buildPath}/compile_commands.json"], "includePath": ["${workspaceFolder}/**"], "browse": {"path": ["${config:idf.espIdfPath}/components", "${config:idf.espIdfPathWin}/components", "${workspaceFolder}"], "limitSymbolsToIncludedHeaders": true}, "cStandard": "${default}", "cppStandard": "${default}", "intelliSenseMode": "windows-gcc-x64", "configurationProvider": "cl.eide"}], "version": 4}