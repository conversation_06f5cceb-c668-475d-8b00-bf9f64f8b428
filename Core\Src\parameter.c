#include "main.h"
#include "delay.h"
#include "mcpwm.h"


u32 data_flash_addr=0x800f000; //for 64k flash version, keep 4k byte for store parameter
//u32 data_flash_addr=0x801f000; //for 128k flash version, keep 4k byte for store parameter

void Init_Driver_State(void)
{
	control_word.all=0x06;
	target_position=0;
	profile_target_position_b=profile_target_position;
}
void Init_System_Parameter(void)
{	
	software_version = SOFTWARE_VERSION;
	Modbus_Addr_Base=0;
	RS485_Addr=1;
	RS485_Protocol=1;
	RS485_Baudrate=38400;
	
	over_voltage=600;
	under_voltage=100;
	chop_voltage=520;
	
	over_temperature=750;
	
	Driver_IIt_Filter=2;
	Driver_IIt_Current=15000;
	
	Driver_IIt_Filter_DC=16;
	Driver_IIt_Current_DC=25000;
	
}
void Init_Motor_Parameter(void)
{
	commutation_current=3000;
	commutation_mode=1;
	commutation_time=1000;
	feedback_resolution=16384;
	poles_num=7;
	motor_code=0;
	phase_dir=-1;
	vel_dir=1;
	tamagawa_dir=-1;
	tamagawa_offset=0;
	feedback_type=8;

}

void Init_Control_Parameter(void)
{
	operation_mode=2;
	control_word.all=0x06;
	Iq_demand=0;
	speed_demand=0;
	position_demand=0;
	target_Iq=0;
	target_speed=0;
	target_position=0;
	
	// kcp=50;
	// kci=1;
	kcp=50;
	kci=1;
	kci_sum_limit=10000000;
	current_in_lpf_a=1000;
	current_out_lpf_a=600;
	Ilim=5000;
	
	// kvp=1500;
	// kvi=30;
	kvp=1300;
	kvi=20;
	kvi_sum_limit=5000;
	low_pass_filter_on=1;
	speed_in_lpf_a=300;
	speed_out_lpf_a=300;
	vel_lim=50000;
	
	// kpp=2;
	// kpi=1;
	kpp=2;
	kpi=0;
	kpi_sum_limit=100;
	position_in_lpf_a=1000;
	position_out_lpf_a=1000;
	
	profile_target_position=0;
	profile_speed=5000;
	profile_acce=1000;
	profile_dece=1000;
	
	searching_speed=1000;
	motion_out_lpf_a=1000;
	
	auto_reverse_p_time=0;
	auto_reverse_n_time=0;
	auto_p_pos=0;
	auto_n_pos=0;
	auto_switch_on=0;
	
	
}

void Exchange_motor_code(void)
{
	char fb,pp,re,cr;
	fb = motor_code/10000;
	pp = (motor_code%10000)/1000;
	re = (motor_code%1000)/10;
	cr = motor_code%10;
	if(fb!=0)
	{
		feedback_type=fb;
	}
	if(pp!=0)
	{
		poles_num=pp;
	}
	if(cr!=0)
	{
		motor_rated_current=cr*2000;
		motor_peak_current=cr*4000;
	}
	switch(re)
	{
		case 1:
			feedback_resolution=1000;
			break;
		case 2:
			feedback_resolution=2000;
			break;
		case 3:
			feedback_resolution=2048;
			break;
		case 4:
			feedback_resolution=4000;
			break;
		case 5:
			feedback_resolution=4096;
			break;
		case 8:
			feedback_resolution=8000;
			break;
		case 9:
			feedback_resolution=8192;
			break;
		case 10:
			feedback_resolution=10000;
			break;
		case 16:
			feedback_resolution=16384;
			break;
		case 32:
			feedback_resolution=32768;
			break;
		case 65:
			feedback_resolution=65536;
			break;
		case 13:
			feedback_resolution=131072;
			break;
		case 26:
			feedback_resolution=262144;
			break;
	}
	
}

/*************************************************************************************************** 
* 函数功能：MemReadByte() 
* 函数说明：从内存中读取num个字节的数据 
* 参数说明： *dat:存储数据的指针地址 
* num :读取的字节数 
* 返回值： 0读取失败，1读取成功 
* 使用说明： 无
* 修改日期：2020年02月06日 
***************************************************************************************************/ 
u8 MemReadHalfWord(u16 *data,u16 vaddr,u16 num) 
{ 
	u16 *temp_addr = (u16 *)(data_flash_addr+vaddr); 
	//*temp_addr =0xaa;
	HAL_FLASH_Unlock();
	while(num --) 
	{ 
	*data ++ = *temp_addr ++; 
	} 
	HAL_FLASH_Lock();
return 1; 
} 	 
u8 MemWriteHalfWord(u16 *data,u16 vaddr,u16 num)
{
	u8 res=0;
	u16 tempnum=0; 
  u32 PageError = 0;
  FLASH_EraseInitTypeDef f;
  f.TypeErase = FLASH_TYPEERASE_PAGES;  // 设置擦除类型为页擦除
  f.PageAddress = data_flash_addr;  // 设置擦除起始地址为数据存储地址
  f.NbPages = 1;  // 设置擦除页数为1页
	
	HAL_FLASH_Unlock();
  HAL_FLASHEx_Erase(&f, &PageError);
	for(tempnum=0;tempnum<num;tempnum++)
		HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD,(data_flash_addr+vaddr+tempnum*2),data[tempnum]);

	HAL_FLASH_Lock();  // 写入完成后锁定FLASH
	return res;  // 返回结果
}

/*************************************************************************************************** 
* 函数功能：MemReadModbus() 
* 函数说明：从FLASH中读取存储在Modbus寄存器中的数据
* 参数说明： modbus_addr: Modbus寄存器的地址起始地址
*           vaddr: FLASH中的地址偏移
*           num: 要读取的数据个数 
* 返回值： 1读取成功 
* 使用说明： 从FLASH中读取存储在Modbus寄存器中的数据
***************************************************************************************************/ 
u8 MemReadModbus(u16 modbus_addr,u16 vaddr,u16 num) 
{ 
	u16 *temp_addr = (u16 *)(data_flash_addr+vaddr);  // 设置FLASH中的起始地址
	//*temp_addr =0xaa;
	HAL_FLASH_Unlock();  // 解锁FLASH写入保护
	while(num --)  // 循环读取指定的数据
	{ 
		*Modbus_Output_Reg[modbus_addr++] = *temp_addr ++;  // 从FLASH中读取数据并写入Modbus寄存器
	} 
	HAL_FLASH_Lock();  // 读取完成后锁定FLASH
	return 1;  // 返回结果
}
	 
/*************************************************************************************************** 
* 函数功能：MemWriteModbus() 
* 函数说明：将Modbus寄存器中的数据写入FLASH
* 参数说明： modbus_addr: Modbus寄存器的地址起始地址
*           vaddr: FLASH中的地址偏移
*           num: 要写入的数据个数 
* 返回值： 0写入成功
* 使用说明： 将Modbus寄存器中的数据写入FLASH存储
***************************************************************************************************/ 
u8 MemWriteModbus(u16 modbus_addr,u16 vaddr,u16 num)
{
	u8 res=0;  // 返回结果
	u16 tempnum=0;  // 临时变量
  u32 PageError = 0;  // 擦除错误
  FLASH_EraseInitTypeDef f;  // FLASH擦除结构体
  f.TypeErase = FLASH_TYPEERASE_PAGES;  // 设置擦除类型为页擦除
  f.PageAddress = data_flash_addr;  // 设置擦除起始地址为数据存储地址
  f.NbPages = 1;  // 设置擦除页数为1页
	
	HAL_FLASH_Unlock();  // 解锁FLASH写入保护
  HAL_FLASHEx_Erase(&f, &PageError);  // 擦除指定的FLASH页
	for(tempnum=0;tempnum<num;tempnum++)
		HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD,(data_flash_addr+vaddr+tempnum*2),*Modbus_Output_Reg[tempnum+modbus_addr]);

	HAL_FLASH_Lock();
	return res; 
}

/*
 * 函数功能：处理参数存储操作
 * 参数说明：无
 * 返回值：无
 * 各分支说明：
 * 1. 存储控制参数到Modbus地址100
 * 2. 初始化控制参数
 * 3. 初始化电机参数
 * 4. 综合初始化系统/电机/控制参数并存储到Modbus
 * 5. 执行系统复位
 */
void Process_Store_parameter(void)
{
	
		switch(store_parameter)
		{
			/* 存储控制参数到Modbus地址100 */
			case 1:
				control_word.all=0x86;
				delay_ms(100);
				MemWriteModbus(100,0,200);
				delay_ms(10);
				store_parameter=0;
			break;

			/* 初始化控制参数 */
			case 2:
				Init_Control_Parameter();
				delay_ms(10);
				store_parameter=0;
			break;

			/* 初始化电机参数 */
			case 3:
				Init_Motor_Parameter();
				delay_ms(10);
				store_parameter=0;
			break;

			/* 综合初始化系统/电机/控制参数并存储到Modbus */
			case 4:
				control_word.all=0x86;
				delay_ms(10);
				Init_System_Parameter();
				Init_Motor_Parameter();
				Init_Control_Parameter();
				MemWriteModbus(100,0,200);
				delay_ms(10);
				store_parameter=0;
			break;

			/* 执行系统复位 */
			case 5:
				HAL_NVIC_SystemReset();
				store_parameter=0;
			break;

			default:
				break;
		}
		
}
