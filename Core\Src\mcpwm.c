/* Includes ------------------------------------------------------------------*/

#include <mcpwm.h>

#include <stdlib.h>
#include <math.h>
//#include <cmsis_os.h>

#include <main.h>
#include <adc.h>
#include <tim.h>
#include <utils.h>
#include "delay.h"
#include "vofa_function.h"

/* Private defines -----------------------------------------------------------*/

/* Private macros ------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
/* Global constant data ------------------------------------------------------*/
/* Global variables ----------------------------------------------------------*/
short vbus_voltage = 120,device_temperature=250; 
int ADCValue[4],ADC_Offset[4];
int Id,Iq,Iq_real,Id_real;
short phase_dir=1,phase_dir_B=1,hall_phase_dir=1,vel_dir=1;
int Iq_demand=0,Id_demand=0,target_Iq=0,target_Id=0;
int target_speed=0,speed_demand=0,target_position=0,target_position_b=0,position_demand;
int commutation_current=2000,motor_rated_current=2000,motor_peak_current=2000,motor_overload_time=1000;
short commutation_founded=0,commutation_mode=0,commutation_time=1000;
int loop_counter_c=0,loop_counter_v=1,loop_counter_p=2,current_loop_ready=0,velocity_loop_ready=0,position_loop_ready=0;
int phase_view;
uint16_t feedback_type=1,poles_num=2,motor_code=0;
int feedback_resolution=4000;
short over_voltage,under_voltage,chop_voltage,over_temperature;
short tamagawa_offset=0,tamagawa_dir=1;
short Driver_Ready=0;
int drv8301_error=0;
unsigned short ENC_Z_Count=0,ENC_Z_Count_B=0,ENC_Z_Count_C=0,ENC_Z_First=0,ENC_Z_Trig=0,ENC_Counting_Error=0;
short ENC_Z_Diff=0,ENC_Z_Diff_B=0;
int ENC_Z_Pos=0,ENC_Z_Pos_B=0,ENC_Z_Pos_Offset=0,ENC_Z_Pos_Diff=0,ENC_Z_Pos_Diff_B=0;

short hall_u=0,hall_v=0,hall_w=0,hall_state=0,hall_state_b=0,hall_error=0;
short hall_count=0,hall_get_position=0,start_calibrate_hall_phase=0;
int encoder_direction_temp=0,encoder_direction_temp_b=0;
short hall_phase[8],ENC_Z_Offset=2680,hall_phase_offset=0,ENC_Z_Phase=0,ENC_Z_Phase_B=0,ENC_Z_Phase_Err=0;
int hall_position=0,hall_position_b=0;
short encoder_offset_diff=0,hall_phase_offset_diff=0;
union error_uint32_t Error_State;
union can_int32_t Scop_Buffer[4][512]={0};
short Scop_Period=31,Scop_Period_counter=0,Scop_Buffer_point=0,Scop_Start=0,Scop_Data_Ready=0,Scop_Send_point=0;
short Scop_Point[4];

int led_blink_counter=0,led_blink_period=1000;

union can_int32_t Scop_Chanel[4];
unsigned char Scop_Send_Buf[20];

u16 store_parameter=0;

Motor_t motor = {  
  .motor_timer = &htim1,
	.PWM1_Duty = TIM_1_8_PERIOD_CLOCKS/2,
	.PWM2_Duty = TIM_1_8_PERIOD_CLOCKS/2,
	.PWM3_Duty = TIM_1_8_PERIOD_CLOCKS/2,
  .control_deadline = TIM_1_8_PERIOD_CLOCKS,
	.PhaseU_current = 0,
	.PhaseV_current = 0,
	.PhaseW_current = 0,
	
  .shunt_conductance = 300,  //100 means 1 mOh, current sensing resistor
  .phase_resistor = 5, //[S]
  .phase_inductance = 5, //[S]
  .encoder_offset = 0,
  .encoder_state = 0,
  .phase = 0.0f, // [rad]
  .pll_pos = 0.0f, // [rad]
  .pll_vel = 0.0f, // [rad/s]
  .pll_kp = 0.0f, // [rad/s / rad]
  .pll_ki = 0.0f // [(rad/s^2) / rad]

};

/* Private constant data -----------------------------------------------------*/
void OC4_PWM_Override(TIM_HandleTypeDef* htim);

/* Function implementations --------------------------------------------------*/

// Initalises the low level motor control and then starts the motor control threads
//这段代码实现了电机控制的初始化，首先进行ADC偏移校准，然后启动ADC，最后启动PWM输出
void init_motor_control(void) 
{
    delay_ms(10);
	Calibrate_ADC_Offset();
    start_adc();
		Driver_Ready=1;
	
    delay_ms(20);
		if((feedback_type==4)||(feedback_type==5))
		{
			if(Tamagawa_First<10)
			{
				Error_State.bits.ENC_error=1;
			}
			else
			{
				tamagawa_angle_b=tamagawa_angle;
				pos_offest= tamagawa_angle;
				motor.encoder_state=pos_offest;
			}
		}
		
		if(feedback_type==8)
		{
			tamagawa_angle_b=tamagawa_angle;
			pos_offest= tamagawa_angle;
			motor.encoder_state=pos_offest;
		}
    delay_ms(10);
}

//@TODO make available from anywhere
void safe_assert(int arg) {
    if(!arg) {
        htim1.Instance->BDTR &= ~(TIM_BDTR_MOE);
        for(;;);
    }
}

void start_adc(void){
    //Enable ADC and interrupts
    __HAL_ADC_ENABLE(&hadc1);
    __HAL_ADC_ENABLE(&hadc2);
    //Warp field stabilize.
    delay_ms(2);
    __HAL_ADC_ENABLE_IT(&hadc1, ADC_IT_JEOC);
    __HAL_ADC_ENABLE_IT(&hadc2, ADC_IT_JEOC);

	HAL_ADCEx_InjectedStart(&hadc1);//need to star a time
	HAL_ADCEx_InjectedStart(&hadc2);//
	
    __HAL_DBGMCU_FREEZE_TIM1();
    //__HAL_DBGMCU_FREEZE_TIM8();

    htim1.Instance->CCR4 = 1;
    HAL_TIM_PWM_Start_IT(&htim1, TIM_CHANNEL_4);
}

void start_pwm(TIM_HandleTypeDef* htim){
    //Init PWM
    int half_load = TIM_1_8_PERIOD_CLOCKS/2;
    htim->Instance->CCR1 = half_load;
    htim->Instance->CCR2 = half_load;
    htim->Instance->CCR3 = half_load;

    //This hardware obfustication layer really is getting on my nerves
    HAL_TIM_PWM_Start(htim, TIM_CHANNEL_1);
    HAL_TIMEx_PWMN_Start(htim, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(htim, TIM_CHANNEL_2);
    HAL_TIMEx_PWMN_Start(htim, TIM_CHANNEL_2);
    HAL_TIM_PWM_Start(htim, TIM_CHANNEL_3);
    HAL_TIMEx_PWMN_Start(htim, TIM_CHANNEL_3);
}

void stop_pwm(TIM_HandleTypeDef* htim){
    //Init PWM
    int half_load = TIM_1_8_PERIOD_CLOCKS/2;
    htim->Instance->CCR1 = half_load;
    htim->Instance->CCR2 = half_load;
    htim->Instance->CCR3 = half_load;

    //This hardware obfustication layer really is getting on my nerves
    HAL_TIM_PWM_Stop(htim, TIM_CHANNEL_1);
    HAL_TIMEx_PWMN_Stop(htim, TIM_CHANNEL_1);
    HAL_TIM_PWM_Stop(htim, TIM_CHANNEL_2);
    HAL_TIMEx_PWMN_Stop(htim, TIM_CHANNEL_2);
    HAL_TIM_PWM_Stop(htim, TIM_CHANNEL_3);
    HAL_TIMEx_PWMN_Stop(htim, TIM_CHANNEL_3);

}

int phase_current_from_adcval(uint32_t ADCValue) 
{
    int amp_gain=AMP_GAIN;

    int amp_out_volt = ONE_ADC_VOLTAGE * ADCValue;
    int shunt_volt = amp_out_volt / amp_gain;
    int current = (shunt_volt*100) / motor.shunt_conductance; // unit mA
    return current;
}

void Calibrate_ADC_Offset(void)
{
    //Enable ADC and interrupts
    __HAL_ADC_ENABLE(&hadc1);
    __HAL_ADC_ENABLE(&hadc2);
    //Warp field stabilize.
    delay_ms(2);
    __HAL_ADC_ENABLE_IT(&hadc1, ADC_IT_JEOC);
    __HAL_ADC_ENABLE_IT(&hadc2, ADC_IT_JEOC);
	
		HAL_ADCEx_InjectedStart(&hadc1);//need to star a time
		HAL_ADCEx_InjectedStart(&hadc2);//
	
    htim1.Instance->CCR4 = 1;
		OC4_PWM_Override(&htim1);  //need to set, otherwise adc can not be trig
		HAL_TIM_PWM_Start_IT(&htim1, TIM_CHANNEL_4);
    delay_ms(200);
    ADC_Offset[0] = ADCValue[0];
    ADC_Offset[1] = ADCValue[1];
		if((ADC_Offset[0]<1800)||(ADC_Offset[0]>2200))
			Error_State.bits.ADC_error=1;
		if((ADC_Offset[1]<1800)||(ADC_Offset[1]>2200))
			Error_State.bits.ADC_error=1;
	
}
int tA, tB, tC;
void queue_modulation_timings(Motor_t* motors, int mod_alpha, int mod_beta) {
    
    SVM(mod_alpha, mod_beta, &tA, &tB, &tC);
    motors->PWM1_Duty = (tC * TIM_1_8_PERIOD_CLOCKS)/1000;
    motors->PWM2_Duty = (tB * TIM_1_8_PERIOD_CLOCKS)/1000;
    motors->PWM3_Duty = (tA * TIM_1_8_PERIOD_CLOCKS)/1000;
	
}

int32_t get_electric_phase(int commutation_current)
{
	int32_t phase_offset;

	Iq_demand=commutation_current;
	delay_ms(commutation_time);
	phase_offset = motor.encoder_state;
	delay_ms(commutation_time);
	return phase_offset;
}
int my_p0,my_p1,my_dir;

//实现电机初始换相角的寻找
void find_commutation(void)
{
	switch(commutation_mode)
	{
		case 0:
			switch(feedback_type)
			{
				case 1:	
				case 4:	
				case 5:			
				case 8:		
					phase_dir=1;    // must set back to the default value
					start_pwm(&htim1);
					motor_on=1;
					motor.phase=0;
					my_p0=get_electric_phase(commutation_current);
					motor.phase=M_PI/2;
					my_p1=get_electric_phase(commutation_current);
					motor.phase=0;
					my_p0=get_electric_phase(commutation_current);
				
					Iq_demand=0;
				
					if(my_p1>=my_p0)
					{
						phase_dir=1;
						if((feedback_resolution/(5*poles_num))<(my_p1-my_p0)&&(my_p1-my_p0)<(feedback_resolution/(3*poles_num)))
						{
							commutation_founded=1;
							motor.encoder_state=0;
							motor.encoder_offset=my_p0%feedback_resolution;
						}
					}
					else
					{
						phase_dir=-1;
						if((feedback_resolution/(5*poles_num))<(my_p0-my_p1)&&(my_p1-my_p0)<(feedback_resolution/(3*poles_num)))
						{
							commutation_founded=1;
							motor.encoder_state=0;
							motor.encoder_offset=feedback_resolution-my_p0%feedback_resolution;
						}
					}
					if((feedback_type==4)||(feedback_type==5)||(feedback_type==8))
					{
						if(commutation_founded==1)
						{
							tamagawa_dir=phase_dir;
							tamagawa_offset=motor.encoder_offset;
						}
					}
					if(commutation_founded==0)
					{
						stop_pwm(&htim1);
				 		motor_on=0;
					}
					break;
				default:
					break;
			}		
			break;
		case 1:
		case 2:
			switch(feedback_type)
			{
				case 1:		
					if(hall_state==0||hall_state==7)
					{
						Error_State.bits.hall_state_error=1;
					}
					if(hall_phase[5]>hall_phase[4])
					{
						hall_phase_dir=-1;
					}
					else
					{
						hall_phase_dir=1;
					}
					motor.encoder_state=0;
					motor.encoder_timer->Instance->CNT=0;
					if(phase_dir==1)
						encoder_offset_diff = (hall_phase[hall_state]+hall_phase_offset)*feedback_resolution/(M_PI*poles_num*2);
					else
						encoder_offset_diff = -feedback_resolution + (hall_phase[hall_state]+hall_phase_offset)*feedback_resolution/(M_PI*poles_num*2);
					
					motor.encoder_offset=-encoder_offset_diff;
				
					motor.encoder_state=0;
					ENC_Z_Count=0;
					ENC_Z_Count_B=0;
					ENC_Z_First=0;
					commutation_founded=1;	
					break;
				case 4:
					phase_dir=tamagawa_dir;
					motor.encoder_state=tamagawa_angle;
					motor.encoder_offset=tamagawa_offset;
					commutation_founded=1;
					break;
				case 5:
					phase_dir=tamagawa_dir;
					motor.encoder_state=tamagawa_angle;
					motor.encoder_offset=tamagawa_offset;
					commutation_founded=1;
					break;
				case 8:
					phase_dir=tamagawa_dir;
					motor.encoder_state=tamagawa_angle;
					motor.encoder_offset=tamagawa_offset;
					commutation_founded=1;			
					break;
				default:
					break;
			}		
			break;
		default:
			break;
	}
	
}

int rad_of_round= 2*M_PI*2;
//电机更新
void update_motor(Motor_t* motors) 
{
	int16_t delta_enc; // 定义编码器变化量
	rad_of_round = 2*M_PI*poles_num; // 设置每电气周期的弧度，与极对数相关
	
	switch(feedback_type) // 根据不同的反馈类型处理编码器数据
	{
		case 0: // 增量式编码器类型
		case 1:
		case 2:
			delta_enc = (int16_t)motors->encoder_timer->Instance->CNT - (int16_t)motors->encoder_state; // 计算编码器计数变化
			motors->encoder_state += (int32_t)delta_enc; // 更新编码器状态
		break;
		
		case 4: // Tamagawa编码器类型
			delta_enc = tamagawa_angle - tamagawa_angle_b; // 计算编码器角度变化
			if(delta_enc<(-feedback_resolution/2)) // 处理角度跨越边界情况（负方向）
				delta_enc+=feedback_resolution;
			if(delta_enc>(feedback_resolution/2)) // 处理角度跨越边界情况（正方向）
				delta_enc-=feedback_resolution;
			tamagawa_angle_b=tamagawa_angle; // 更新上一次的角度值
			motors->encoder_state += (int32_t)delta_enc; // 更新编码器状态
			break;
			
		case 5: // 另一种Tamagawa编码器类型
			delta_enc = tamagawa_angle - tamagawa_angle_b; // 计算编码器角度变化
			if(delta_enc<(-feedback_resolution/2))
				delta_enc+=feedback_resolution;
			if(delta_enc>(feedback_resolution/2))
				delta_enc-=feedback_resolution;
			tamagawa_angle_b=tamagawa_angle;
			motors->encoder_state += (int32_t)delta_enc;
			break;
			
		case 8: // 其他编码器类型
			delta_enc = tamagawa_angle - tamagawa_angle_b;
			if(delta_enc<(-feedback_resolution/2))
				delta_enc+=feedback_resolution;
			if(delta_enc>(feedback_resolution/2))
				delta_enc-=feedback_resolution;
			tamagawa_angle_b=tamagawa_angle;
			motors->encoder_state += (int32_t)delta_enc;    
			break;
			
		default:
			break;
	}

	int ph; // 定义电机电角度变量
	// 计算电气角度，根据编码器位置和方向确定
	if(phase_dir==1) // 正向旋转
		ph= M_PI/2 + (rad_of_round * ((motors->encoder_state % feedback_resolution) - motors->encoder_offset))/feedback_resolution;
	else // 反向旋转
		ph= M_PI/2 + (rad_of_round * ((feedback_resolution-(motors->encoder_state % feedback_resolution)) - motors->encoder_offset))/feedback_resolution;
	
	ph = ph%(2*M_PI); // 将角度限制在0-2π范围内
	
	switch(operation_mode) // 根据不同的运行模式设置电机相位
	{  
		case 1: // 不同的控制模式（如速度控制、位置控制等）
		case 3:
		case 4:
		case 2:
		case 5:
		case 7:            
			if(commutation_founded) // 如果已找到换相角
				motors->phase = ph; // 设置电机相位
			break;
		default:
			break; 
	}
}

