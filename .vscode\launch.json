{"version": "0.2.0", "configurations": [{"type": "gdbtarget", "request": "attach", "name": "Eclipse CDT GDB Adapter"}, {"type": "espidf", "name": "Launch", "request": "launch"}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Desktop/my_project/foc_can/Source_code/Core", "program": "c:/Users/<USER>/Desktop/my_project/foc_can/Source_code/Core/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}