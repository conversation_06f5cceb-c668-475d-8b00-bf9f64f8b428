#include "vofa_function.h"
#include <stdio.h>
#include <stdlib.h>   // 添加标准库头文件，用于atof函数
#include <string.h>
#include "usart.h"
#include "mcpwm.h"  // 包含控制参数变量声明
#include "low_level.h"  // 包含control_word结构声明
#include <stdarg.h>   // 添加可变参数头文件，用于vofaSendMixedDataFirewater函数

// 重定向printf到UART2
int fputc(int ch, FILE *f)
{
    // 通过UART2发送一个字符
    HAL_UART_Transmit(&huart2, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

/*VOFA type structure*/
vofaJustFloatFrame JustFloat_Data;
vofaCommand        vofaCommandData;
uint8_t            vofaRxBufferIndex = 0;

// 添加一个缓冲区用于DMA发送
uint8_t vofaTxBuffer[CH_COUNT * 4 + FRAME_TAIL_SIZE]; // 用于存储要发送的所有数据

void uartSendByte(const uint8_t c)
{
	HAL_UART_Transmit_DMA(&huart2, (uint8_t*)&c, 1); //修改为串口DMA发送接口
}

void uartSendData(uint8_t* Array, uint8_t SIZE)
{
	HAL_UART_Transmit_DMA(&huart2, Array, SIZE); //使用DMA直接发送整个数组
}
/**
* @param vofaJFFframe: 包含数据帧的结构体
* @return void
*/
void vofaSendJustFloat(vofaJustFloatFrame* vofaJFFrame)
{
	uint8_t i;
	uint16_t bufferIndex = 0;
	
	// 将所有数据打包到缓冲区
	for (i = 0; i < CH_COUNT; i++)
	{
		float2uint8Array(&vofaTxBuffer[bufferIndex], &vofaJFFrame->fdata[i]);
		bufferIndex += 4;
	}
	
	// 添加帧尾
	memcpy(&vofaTxBuffer[bufferIndex], vofaJFFrame->frametail, FRAME_TAIL_SIZE);
	bufferIndex += FRAME_TAIL_SIZE;
	
	// 一次性发送所有数据
	HAL_UART_Transmit_DMA(&huart2, vofaTxBuffer, bufferIndex);
}

/**
* @param fdata: 指向要发送的浮点数据的指针
* @param ulSize： 要发送的数据个数
* @return void
*/
void vofaSendFirewater(const float* fdata, const uint32_t ulSize)
{
	static char buffer[256]; // 静态缓冲区用于存储格式化后的字符串
	uint32_t i;
	uint32_t offset = 0;
	
	// 格式化数据到缓冲区
	for (i = 0; i < ulSize - 1; i++)
	{
		offset += snprintf(buffer + offset, sizeof(buffer) - offset, "%.6f,", *(fdata + i));
		if (offset >= sizeof(buffer) - 20) // 留出足够空间给最后一个值和换行符
			break;
	}
	
	if (i < ulSize) // 添加最后一个值和换行符
	{
		offset += snprintf(buffer + offset, sizeof(buffer) - offset, "%.6f\n", *(fdata + i));
	}
	
	// 使用DMA发送格式化后的字符串
	HAL_UART_Transmit_DMA(&huart2, (uint8_t*)buffer, offset);
}

/**
* @param pData: 指向要发送的单字节数据的指针
* @param ulSize： 要发送的数据个数
* @return void
*/
void vofaSendRawdata(uint8_t* pData, const uint32_t ulSize)
{
	HAL_UART_Transmit_DMA(&huart2, pData, ulSize);
}

float uint8Array2Float(const uint8_t* u8Array)
{
	float   fdata = 0;
	uint8_t Data[4];

	Data[0] = u8Array[0];
	Data[1] = u8Array[1];
	Data[2] = u8Array[2];
	Data[3] = u8Array[3];

	memcpy(&fdata, Data, 4);
	return fdata;
}

void float2uint8Array(uint8_t* u8Array, const float* fdata)
{
	uint8_t floatArray[4];
	*(float*)floatArray = *fdata;

	u8Array[0] = floatArray[0];
	u8Array[1] = floatArray[1];
	u8Array[2] = floatArray[2];
	u8Array[3] = floatArray[3];

}

/**
* @brief 初始化JustFloat帧结构体
*/
void vofaJustFloatInit(void)
{
	vofaCommandData.cmdID          = INVALID;
	vofaCommandData.cmdType        = INVALID;
	vofaCommandData.completionFlag = 0;
	JustFloat_Data.frametail[0]    = 0x00;
	JustFloat_Data.frametail[1]    = 0x00;
	JustFloat_Data.frametail[2]    = 0x80;
	JustFloat_Data.frametail[3]    = 0x7f;
}

/**
* @brief 将串口收到的数据判断并存入数据包中，并比对帧控制接收完成标志位置位
* @param byte_data： 串口接收到的字节数据 
*/
void uartCMDRecv(uint8_t byte_data) //此函数放在串口中断中
{
    // 安全检查：确保索引在有效范围内
    if(vofaRxBufferIndex >= CMD_FRAME_SIZE) {
        vofaRxBufferIndex = 0;
        memset(vofaCommandData.uartRxPacket, 0, CMD_FRAME_SIZE);
    }
    
    // 保存当前字节
    vofaCommandData.uartRxPacket[vofaRxBufferIndex] = byte_data;

    // 检测命令帧是否完成（检查!#结尾）
    if (vofaRxBufferIndex > 0 && 
        vofaCommandData.uartRxPacket[vofaRxBufferIndex-1] == '!' && 
        vofaCommandData.uartRxPacket[vofaRxBufferIndex] == '#')
    {
        // 设置命令完成标志，但不要重置索引，以便在回传时能获取完整命令
        vofaCommandData.completionFlag = 1;
        // 注意：这里不重置vofaRxBufferIndex，让中断处理函数先完成回传
    }
    else
    {
        // 继续接收下一个字节
        vofaRxBufferIndex++;
    }
}

/**
* @brief vofa命令帧解析
*/
void vofaCommandParse(void)
{
	// 检查命令格式：必须以@开头，第4个字符必须是=
	if (vofaCommandData.uartRxPacket[0] != '@' || vofaCommandData.uartRxPacket[3] != '=')
	{
		memset(vofaCommandData.uartRxPacket, 0, CMD_FRAME_SIZE);
		return;
	}
	
	// 查找命令结束标记!#（不再假设固定位置）
	int i;
	int foundEnd = 0;
	for(i = 4; i < CMD_FRAME_SIZE - 1; i++) {
		if(vofaCommandData.uartRxPacket[i] == '!' && vofaCommandData.uartRxPacket[i+1] == '#') {
			foundEnd = 1;
			break;
		}
	}
	
	if(!foundEnd) {
		memset(vofaCommandData.uartRxPacket, 0, CMD_FRAME_SIZE);
		return;
	}

	// 解析命令类型
	switch (vofaCommandData.uartRxPacket[1])
	{
		case 'S': 
			if (vofaCommandData.uartRxPacket[2] == '1') vofaCommandData.cmdType = SpeedLoopKp;
			else if (vofaCommandData.uartRxPacket[2] == '2') vofaCommandData.cmdType = SpeedLoopKi;
			else vofaCommandData.cmdType = Speed;
			break;
		case 'P': 
			if (vofaCommandData.uartRxPacket[2] == '1') vofaCommandData.cmdType = PositionLoopKp;
			else if (vofaCommandData.uartRxPacket[2] == '2') vofaCommandData.cmdType = PositionLoopKi;
			else vofaCommandData.cmdType = Position;
			break;
		case 'C': 
			if (vofaCommandData.uartRxPacket[2] == '1') vofaCommandData.cmdType = CurrentLoopKp;
			else if (vofaCommandData.uartRxPacket[2] == '2') vofaCommandData.cmdType = CurrentLoopKi;
			else vofaCommandData.cmdType = Current;
			break;
		case 'O':
			if (vofaCommandData.uartRxPacket[2] == 'F') vofaCommandData.cmdType = MotorEnable;
			else vofaCommandData.cmdType = INVALID;
			break;
		case 'M':
			if (vofaCommandData.uartRxPacket[2] == '1') vofaCommandData.cmdType = Current;
			else if (vofaCommandData.uartRxPacket[2] == '2') vofaCommandData.cmdType = Speed;
			else if (vofaCommandData.uartRxPacket[2] == '3') vofaCommandData.cmdType = Position;
			else vofaCommandData.cmdType = INVALID;
			break;
		default: vofaCommandData.cmdType = INVALID;
			break;
	}

	// 对于特殊的命令类型，已经在上面的switch中处理了cmdType
	// 对于其他命令，需要解析cmdID
	// if (vofaCommandData.cmdType == Speed || vofaCommandData.cmdType == Position || vofaCommandData.cmdType == Current)
	// {
	// 	switch (vofaCommandData.uartRxPacket[2])
	// 	{
	// 		case '1': vofaCommandData.cmdID = Direct_Assignment;
	// 			break;
	// 		case '2': vofaCommandData.cmdID = Increase;
	// 			break;
	// 		case '3': vofaCommandData.cmdID = Decrease;
	// 			break;
	// 		default: vofaCommandData.cmdID = INVALID;
	// 			break;
	// 	}
	// }
	// else
	// {
	// 	// 对于参数设置类命令，直接赋值
	// 	vofaCommandData.cmdID = Direct_Assignment;
	// }

	// 提取浮点数据（从ASCII字符串中解析）
	char valueStr[20] = {0}; // 用于存储数值的字符串
	int valuePos = 4; // 从第4个字符开始（跳过@X=）
	int valueLen = 0;
	
	// 提取等号后面到感叹号之前的所有字符作为数值字符串
	while(vofaCommandData.uartRxPacket[valuePos] != '!' && valuePos < CMD_FRAME_SIZE - 2) {
		valueStr[valueLen++] = vofaCommandData.uartRxPacket[valuePos++];
	}
	valueStr[valueLen] = '\0'; // 字符串结束符
	
	// 将字符串转换为浮点数
	vofaCommandData.floatData = atof(valueStr);
	
	// 根据命令类型直接修改对应的控制参数
	if(vofaCommandData.cmdType != INVALID) {
		switch (vofaCommandData.cmdType) {
			case MotorEnable:
				control_word.all = (vofaCommandData.floatData > 0) ? 0x0F : 0x06;
				break;
			case CurrentLoopKp:
				kcp = vofaCommandData.floatData;
				// printf("设置电流环Kp = %.2f\r\n", kcp); // 调试输出
				break;
			case CurrentLoopKi:
				kci = vofaCommandData.floatData;
				break;
			case SpeedLoopKp:
				kvp = vofaCommandData.floatData;
				break;
			case SpeedLoopKi:
				kvi = vofaCommandData.floatData;
				break;
			case PositionLoopKp:
				kpp = vofaCommandData.floatData;
				break;
			case PositionLoopKi:
				kpi = vofaCommandData.floatData;
				break;
			case Current:
				// target_Iq = vofaCommandData.floatData;
				Id_demand = vofaCommandData.floatData;
				break;
			case Speed:
				target_speed = vofaCommandData.floatData;
				break;
			case Position:
				target_position = vofaCommandData.floatData;
				break;
			default:
				break;
		}
	}

	// 清理缓冲区
	memset(vofaCommandData.validData, 0, 4);
	memset(vofaCommandData.uartRxPacket, 0, CMD_FRAME_SIZE);
	
	// 重置接收索引
	vofaRxBufferIndex = 0;
}

/**
* @brief 发送uint16类型数据到VOFA（JustFloat协议）
* @param uint16_data: 指向要发送的uint16数据的指针
* @param count: 要发送的数据个数
* @return void
*/
void vofaSendUint16AsFloat(const uint16_t* uint16_data, uint8_t count)
{
    uint8_t i;
       
    // 将uint16数据转换为float并存入JustFloat结构体
    for(i = 0; i < count; i++)
    {
        JustFloat_Data.fdata[i] = (float)uint16_data[i];
    }
    
    // 发送JustFloat格式数据
    vofaSendJustFloat(&JustFloat_Data);
}

/**
* @brief 发送int类型数据到VOFA（JustFloat协议）
* @param int_data: 指向要发送的int数据的指针
* @param count: 要发送的数据个数
* @return void
*/
void vofaSendIntAsFloat(const int* int_data, uint8_t count)
{
    uint8_t i;
      
    // 将int数据转换为float并存入JustFloat结构体
    for(i = 0; i < count; i++)
    {
        JustFloat_Data.fdata[i] = (float)int_data[i];
    }
   
    // 发送JustFloat格式数据
    vofaSendJustFloat(&JustFloat_Data);
}

/**
* @brief 发送int32_t类型数据到VOFA（JustFloat协议）
* @param int32_data: 指向要发送的int32_t数据的指针
* @param count: 要发送的数据个数
* @return void
*/
void vofaSendInt32AsFloat(const int32_t* int32_data, uint8_t count)
{
    uint8_t i;
       
    // 将int32_t数据转换为float并存入JustFloat结构体
    for(i = 0; i < count; i++)
    {
        JustFloat_Data.fdata[i] = (float)int32_data[i];
    }
    
    // 清零剩余的通道
    for(; i < CH_COUNT; i++)
    {
        JustFloat_Data.fdata[i] = 0.0f;
    }
    
    // 发送JustFloat格式数据
    vofaSendJustFloat(&JustFloat_Data);
}

/**
* @brief 发送混合类型数据到VOFA（JustFloat协议）
* @param values: 指向float类型数组的指针，存储所有转换后的数据
* @param count: 数据个数（必须小于或等于CH_COUNT）
* @return void
*/
void vofaSendMixedDataAsFloat(const float* values, uint8_t count)
{
    uint8_t i;
    
    for(i = 0; i < count; i++)
    {
        JustFloat_Data.fdata[i] = values[i];
    }
    // 发送JustFloat格式数据
    vofaSendJustFloat(&JustFloat_Data);
}

